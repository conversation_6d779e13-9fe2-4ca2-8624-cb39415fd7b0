#!/usr/bin/env python3
"""
安全测试脚本 - 验证测试环境的安全性
确保测试用户无法获取源代码和敏感信息
"""

import os
import subprocess
import sys
import time
from pathlib import Path

import requests


def run_command(cmd: list[str]) -> tuple[bool, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True, timeout=30
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr
    except subprocess.TimeoutExpired:
        return False, "Command timed out"


def test_source_code_exposure():
    """测试源代码暴露风险"""
    print("\n🔍 测试源代码暴露风险...")
    
    # 检查是否有源码挂载
    success, output = run_command(["docker", "compose", "-f", "docker-compose.yml", "config"])
    if not success:
        print("❌ 无法读取 docker-compose 配置")
        return False
    
    if "./:/app" in output:
        print("❌ 发现源码挂载！测试环境仍有源码泄露风险")
        return False
    
    print("✅ 生产配置中无源码挂载")
    return True


def test_api_docs_disabled():
    """测试API文档是否被禁用"""
    print("\n🔍 测试API文档访问...")
    
    # 等待服务启动
    time.sleep(5)
    
    try:
        # 测试 /docs 端点
        response = requests.get("http://localhost:8000/docs", timeout=10)
        if response.status_code == 200:
            print("❌ API文档仍然可访问！")
            return False
        
        # 测试 /redoc 端点
        response = requests.get("http://localhost:8000/redoc", timeout=10)
        if response.status_code == 200:
            print("❌ ReDoc文档仍然可访问！")
            return False
        
        print("✅ API文档已被禁用")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 无法连接到服务: {e}")
        return False


def test_environment_variables():
    """测试环境变量安全性"""
    print("\n🔍 测试环境变量安全性...")
    
    # 检查 .env 文件是否在 .dockerignore 中
    dockerignore_path = Path(".dockerignore")
    if not dockerignore_path.exists():
        print("❌ .dockerignore 文件不存在")
        return False
    
    dockerignore_content = dockerignore_path.read_text()
    if ".env" not in dockerignore_content:
        print("❌ .env 文件未在 .dockerignore 中排除")
        return False
    
    print("✅ .env 文件已在 .dockerignore 中排除")
    return True


def test_frontend_source_maps():
    """测试前端 Source Maps 是否被禁用"""
    print("\n🔍 测试前端 Source Maps...")
    
    try:
        # 检查前端是否返回 .map 文件
        response = requests.get("http://localhost:8000/assets/index.js.map", timeout=10)
        if response.status_code == 200:
            print("❌ Source Maps 仍然可访问！")
            return False
        
        print("✅ Source Maps 已被禁用")
        return True
        
    except requests.exceptions.RequestException:
        print("✅ Source Maps 不可访问")
        return True


def test_sensitive_files():
    """测试敏感文件访问"""
    print("\n🔍 测试敏感文件访问...")
    
    sensitive_paths = [
        "/.env",
        "/docker-compose.yml",
        "/backend/config/settings.py",
        "/.git/config",
        "/pyproject.toml",
        "/package.json"
    ]
    
    for path in sensitive_paths:
        try:
            response = requests.get(f"http://localhost:8000{path}", timeout=5)
            if response.status_code == 200:
                print(f"❌ 敏感文件可访问: {path}")
                return False
        except requests.exceptions.RequestException:
            pass  # 这是期望的行为
    
    print("✅ 敏感文件无法访问")
    return True


def test_container_isolation():
    """测试容器隔离"""
    print("\n🔍 测试容器隔离...")
    
    # 检查是否使用了生产配置
    success, output = run_command([
        "docker", "compose", "-f", "docker-compose.yml", "ps", "--format", "json"
    ])
    
    if not success:
        print("❌ 无法检查容器状态")
        return False
    
    print("✅ 容器隔离测试通过")
    return True


def main():
    """主测试函数"""
    print("🛡️ 开始安全测试...")
    print("=" * 50)
    
    tests = [
        ("源代码暴露", test_source_code_exposure),
        ("API文档禁用", test_api_docs_disabled),
        ("环境变量安全", test_environment_variables),
        ("前端Source Maps", test_frontend_source_maps),
        ("敏感文件访问", test_sensitive_files),
        ("容器隔离", test_container_isolation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 安全测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有安全测试通过！测试环境相对安全。")
        return 0
    else:
        print("⚠️ 部分安全测试失败，请检查配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
