FROM python:3.11-slim


# 设置工作目录
WORKDIR /app



# 复制依赖文件
COPY pyproject.toml .

# 先安装基础依赖（不包括当前项目）
# 跳过 Playwright 安装，仅用于测试安全性
RUN pip install --no-cache-dir build

# 复制应用代码
COPY . .

# 安装当前项目
RUN pip install --no-cache-dir -e .

# 创建必要的目录
RUN mkdir -p screenshots payment_screenshots downloads .prefs test_data results

# 设置环境变量
ENV PYTHONUNBUFFERED=1


# 设置时区为UTC（数据库层统一时区重构）
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# 健康检查端点 开发阶段 取消健康检查



# 默认启动新版FastAPI (可被docker-compose.yml覆盖)
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
